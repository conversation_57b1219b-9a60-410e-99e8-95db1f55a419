#!/usr/bin/env python3
"""
Writing Tools - Test Script for Bug Fixes
Tests the fixes for terminate_existing_processes and data.json location
"""

import os
import sys
import tempfile
import shutil
import json
import logging
from pathlib import Path

# Enable debug logging
logging.basicConfig(level=logging.DEBUG, format="%(levelname)s: %(message)s")

# Add the parent directory to the path to import config modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import SettingsManager
from config.interfaces import SystemConfig, ActionConfig, UnifiedSettings
from config.constants import DEFAULT_SETTINGS, DEFAULT_SYSTEM, DEFAULT_ACTIONS


def test_data_json_location():
    """Test that data.json is created in the correct location for different modes"""
    print("\n=== Testing data.json location logic ===")

    # Create a temporary directory structure
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"Using temp directory: {temp_dir}")

        # Create structure
        os.makedirs(f"{temp_dir}/config", exist_ok=True)
        os.makedirs(f"{temp_dir}/config/config_sauv", exist_ok=True)

        # Create default_data.json
        default_data = {
            "system": {
                "api_key": "",
                "provider": "",
                "model": "",
                "hotkey": "ctrl+space",
            },
            "actions": {},
            "custom_data": {},
        }

        with open(f"{temp_dir}/config/default_data.json", "w") as f:
            json.dump(default_data, f, indent=2)

        # Test 1: build-final mode with existing data.json
        print("\n--- Test 1: build-final mode with existing data.json ---")
        with open(f"{temp_dir}/data.json", "w") as f:
            json.dump({"test": "existing"}, f)

        # Change to temp directory and test
        original_cwd = os.getcwd()
        try:
            os.chdir(temp_dir)

            # Mock sys.frozen and sys.executable
            import sys

            original_frozen = getattr(sys, "frozen", False)
            original_executable = getattr(sys, "executable", "")

            sys.frozen = True
            sys.executable = f"{temp_dir}/Writing Tools.exe"

            settings_manager = SettingsManager(mode="build-final")
            print(f"Mode: {settings_manager.mode}")
            print(f"Base dir: {settings_manager.base_dir}")
            print(f"Data file: {settings_manager.data_file}")

            # Should use existing data.json in base directory
            assert str(settings_manager.data_file) == str(Path(temp_dir) / "data.json")
            print("✓ Correct data file path for build-final with existing data.json")

            # Test 2: build-final mode without existing data.json
            print("\n--- Test 2: build-final mode without existing data.json ---")
            os.remove(f"{temp_dir}/data.json")

            settings_manager2 = SettingsManager(mode="build-final")
            print(f"Data file: {settings_manager2.data_file}")

            # Should still point to base directory for new file creation
            assert str(settings_manager2.data_file) == str(Path(temp_dir) / "data.json")
            print("✓ Correct data file path for build-final without existing data.json")

            # Test saving settings
            settings_manager2.load_settings()
            settings_manager2.save_settings()

            # Check that data.json was created in the right place
            assert os.path.exists(f"{temp_dir}/data.json")
            assert not os.path.exists(f"{temp_dir}/config/data.json")
            print("✓ data.json created in correct location (base dir, not config dir)")

            # Test 3: dev mode
            print("\n--- Test 3: dev mode ---")
            sys.frozen = False
            sys.executable = original_executable

            # For dev mode, we need to mock sys.argv[0] to point to temp_dir
            original_argv0 = sys.argv[0]
            sys.argv[0] = f"{temp_dir}/main.py"

            settings_manager3 = SettingsManager(mode="dev")
            print(f"Data file: {settings_manager3.data_file}")

            # Should use config_sauv directory
            expected_path = str(Path(temp_dir) / "config" / "config_sauv" / "data.json")
            actual_path = str(settings_manager3.data_file)
            print(f"Expected: {expected_path}")
            print(f"Actual: {actual_path}")
            assert (
                actual_path == expected_path
            ), f"Expected {expected_path}, got {actual_path}"
            print("✓ Correct data file path for dev mode")

            # Restore sys.argv[0]
            sys.argv[0] = original_argv0

            # Restore original values
            sys.frozen = original_frozen
            sys.executable = original_executable

        finally:
            os.chdir(original_cwd)


def test_default_values():
    """Test that default values are empty as expected"""
    print("\n=== Testing default values ===")

    # Test DEFAULT_SYSTEM
    assert (
        DEFAULT_SYSTEM.provider == ""
    ), f"Expected empty provider, got '{DEFAULT_SYSTEM.provider}'"
    assert (
        DEFAULT_SYSTEM.model == ""
    ), f"Expected empty model, got '{DEFAULT_SYSTEM.model}'"
    assert (
        DEFAULT_SYSTEM.api_key == ""
    ), f"Expected empty api_key, got '{DEFAULT_SYSTEM.api_key}'"
    print("✓ DEFAULT_SYSTEM has empty provider, model, and api_key")

    # Test default_data.json content
    default_data_path = Path(__file__).parent.parent / "config" / "default_data.json"
    if default_data_path.exists():
        with open(default_data_path, "r") as f:
            data = json.load(f)

        system = data.get("system", {})
        assert (
            system.get("provider") == ""
        ), f"Expected empty provider in default_data.json, got '{system.get('provider')}'"
        assert (
            system.get("model") == ""
        ), f"Expected empty model in default_data.json, got '{system.get('model')}'"
        print("✓ default_data.json has empty provider and model")
    else:
        print("⚠ default_data.json not found, skipping file test")


def test_settings_manager_debug():
    """Test that debug logging works in SettingsManager"""
    print("\n=== Testing SettingsManager debug logging ===")

    # Create a temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create minimal structure
        os.makedirs(f"{temp_dir}/config", exist_ok=True)

        # Create default_data.json
        default_data = {
            "system": {"api_key": "", "provider": "", "model": ""},
            "actions": {},
            "custom_data": {},
        }
        with open(f"{temp_dir}/config/default_data.json", "w") as f:
            json.dump(default_data, f)

        original_cwd = os.getcwd()
        try:
            os.chdir(temp_dir)

            # Mock executable mode
            import sys

            original_frozen = getattr(sys, "frozen", False)
            original_executable = getattr(sys, "executable", "")

            sys.frozen = True
            sys.executable = f"{temp_dir}/Writing Tools.exe"

            print("Creating SettingsManager with debug logging...")
            settings_manager = SettingsManager(mode="build-final")

            print("Loading and saving settings...")
            settings_manager.load_settings()
            settings_manager.save_settings()

            print("✓ SettingsManager debug logging completed")

            # Restore
            sys.frozen = original_frozen
            sys.executable = original_executable

        finally:
            os.chdir(original_cwd)


def main():
    """Run all tests"""
    print("Starting WritingTools bug fix tests...")

    try:
        test_default_values()
        test_data_json_location()
        test_settings_manager_debug()

        print("\n🎉 All tests passed!")
        return 0

    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback

        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
